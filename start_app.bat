@echo off
chcp 65001 >nul
title RealtimeVoiceChat 启动器

echo ====================================================
echo 🎤 RealtimeVoiceChat 启动器
echo ====================================================
echo.

:: 检查是否在正确的目录
if not exist "code\server.py" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    echo 当前目录应包含 code\server.py 文件
    pause
    exit /b 1
)

:: 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 错误: 虚拟环境不存在
    echo 请先运行以下命令创建虚拟环境:
    echo   python -m venv venv
    echo   venv\Scripts\activate
    echo   pip install -r requirements.txt
    pause
    exit /b 1
)

echo ✅ 找到虚拟环境

:: 激活虚拟环境
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

:: 检查 Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Python 不可用
    pause
    exit /b 1
)

echo ✅ Python 环境正常

:: 检查关键依赖
echo 🔄 检查依赖...
python -c "import torch, fastapi" >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 缺少关键依赖
    echo 请运行: pip install -r requirements.txt
    pause
    exit /b 1
)

echo ✅ 依赖检查完成

:: 检查 .env 文件
if exist ".env" (
    echo ✅ 找到 .env 配置文件
) else (
    echo ⚠️  .env 配置文件不存在，将使用默认配置
    echo 如需使用第三方 LLM 平台，请创建 .env 文件并配置 API 密钥
)

echo.
echo 🚀 启动服务器...
echo 服务器将在 http://localhost:8000 运行
echo 按 Ctrl+C 停止服务器
echo ----------------------------------------

:: 切换到 code 目录并启动服务器
cd code
python server.py

:: 如果服务器退出，暂停以显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 服务器启动失败
    pause
)

echo.
echo 👋 服务器已停止
pause
