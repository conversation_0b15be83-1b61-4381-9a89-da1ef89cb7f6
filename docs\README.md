# RealtimeVoiceChat 项目文档

欢迎查阅 RealtimeVoiceChat 项目文档。本文档提供了项目的架构、功能、接口以及安装使用指南，帮助您快速了解和使用本项目。

## 文档目录

### 1. 项目概述

- [项目架构](./项目架构.md) - 详细介绍项目的整体架构、核心组件、数据流和技术栈

### 2. 功能说明

- [功能说明](./功能说明.md) - 详细介绍项目的核心功能、高级特性和技术特点

### 3. 接口文档

- [接口文档](./接口文档.md) - 详细介绍项目的 WebSocket 接口、HTTP API 和前端 JavaScript API

### 4. 安装使用指南

- [Windows 安装使用指南](./Windows安装使用指南.md) - Windows 10 系统上的安装、配置和使用指南
- [Linux 安装使用指南](./Linux安装使用指南.md) - Linux 系统上的安装、配置和使用指南

## 快速入门

如果您是首次使用本项目，建议按照以下步骤开始：

1. 阅读 [项目架构](./项目架构.md) 了解项目的整体设计
2. 查看 [功能说明](./功能说明.md) 了解项目的主要功能
3. 根据您的操作系统选择相应的安装指南：
   - [Windows 安装使用指南](./Windows安装使用指南.md)
   - [Linux 安装使用指南](./Linux安装使用指南.md)
4. 如果您需要进行二次开发，请参考 [接口文档](./接口文档.md)

## 文档更新

本文档将随项目的发展不断更新。如果您发现文档中的错误或有改进建议，请提交 Issue 或 Pull Request。

## 许可证

本项目文档采用与项目相同的许可证。请参阅项目根目录中的 LICENSE 文件了解详情。