# RealtimeVoiceChat Windows 10 安装使用指南

## 1. 系统要求

### 1.1 硬件要求

- **处理器**：建议 Intel Core i5/AMD Ryzen 5 或更高
- **内存**：最低 8GB RAM，建议 16GB 或更高
- **存储**：至少 10GB 可用空间
- **GPU**：
  - 推荐：NVIDIA GPU（支持 CUDA）
  - 最低：GeForce GTX 1060 6GB 或同等性能
  - 理想：GeForce RTX 系列（更好的性能）

### 1.2 软件要求

- **操作系统**：Windows 10 64位（1909或更高版本）
- **Python**：Python 3.10（不支持 Python 3.13）
- **浏览器**：Chrome、Edge 或 Firefox 最新版本
- **CUDA**：如使用 GPU，需要 CUDA 12.1（Docker 方式会自动安装）

## 2. 安装方法

### 2.1 使用 Docker 安装（推荐）

使用 Docker 是最简单的安装方法，它会自动处理所有依赖项。

#### 2.1.1 安装 Docker Desktop

1. 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. 安装过程中，确保选择「使用 WSL 2」选项
3. 安装完成后重启计算机
4. 启动 Docker Desktop 并等待其完全初始化

#### 2.1.2 安装 NVIDIA Container Toolkit（如使用 NVIDIA GPU）

1. 确保已安装最新的 [NVIDIA 显卡驱动](https://www.nvidia.com/download/index.aspx)
2. 下载并安装 [NVIDIA Container Toolkit for Windows](https://docs.nvidia.com/cuda/wsl-user-guide/index.html)

#### 2.1.3 克隆项目

```powershell
git clone https://github.com/yourusername/RealtimeVoiceChat.git
cd RealtimeVoiceChat
```

如果没有 Git，可以直接从项目页面下载 ZIP 文件并解压。

#### 2.1.4 配置环境变量

创建 `.env` 文件（或复制 `.env.example` 并重命名为 `.env`），设置以下变量：

```
# 必需设置
OPENAI_API_KEY=your_openai_api_key_here  # 如果使用 OpenAI API

# 可选设置
DEFAULT_LLM_PROVIDER=ollama  # ollama, openai, lmstudio
DEFAULT_LLM_MODEL=llama3     # 模型名称
DEFAULT_TTS_ENGINE=kokoro    # kokoro, coqui, orpheus
DEFAULT_TTS_VOICE=v2/en_US/cori/medium  # TTS 语音
```

#### 2.1.5 启动 Docker 容器

```powershell
docker-compose up
```

首次启动时，Docker 会下载和构建所需的镜像，这可能需要一些时间。

### 2.2 手动安装

如果您不想使用 Docker，也可以手动安装。

#### 2.2.1 安装 Python 3.10

1. 从 [Python 官网](https://www.python.org/downloads/release/python-3109/) 下载 Python 3.10
2. 安装时勾选「Add Python to PATH」选项
3. 完成安装后，打开命令提示符验证安装：

```powershell
python --version  # 应显示 Python 3.10.x
```

#### 2.2.2 安装 CUDA（如使用 NVIDIA GPU）

1. 下载并安装 [CUDA Toolkit 12.1](https://developer.nvidia.com/cuda-12-1-0-download-archive)
2. 按照安装向导完成安装

#### 2.2.3 克隆项目

```powershell
git clone https://github.com/yourusername/RealtimeVoiceChat.git
cd RealtimeVoiceChat
```

#### 2.2.4 创建虚拟环境并安装依赖

使用项目提供的安装脚本：

```powershell
.\install.bat
```

或手动执行以下步骤：

```powershell
python -m venv venv
.\venv\Scripts\activate
pip install --upgrade pip
pip install torch==2.1.0+cu121 -f https://download.pytorch.org/whl/torch_stable.html
pip install -r requirements.txt
```

#### 2.2.5 配置环境变量

创建 `.env` 文件，设置与 Docker 安装相同的环境变量。

#### 2.2.6 安装 Ollama（如使用 Ollama 作为 LLM 提供商）

1. 从 [Ollama 官网](https://ollama.ai/download) 下载 Windows 版本
2. 安装 Ollama
3. 拉取所需模型：

```powershell
ollama pull llama3
```

## 3. 启动应用

### 3.1 Docker 方式启动

如果您使用 Docker 安装，应用已经在运行。如果需要重新启动：

```powershell
docker-compose up
```

### 3.2 手动方式启动

1. 激活虚拟环境（如果尚未激活）：

```powershell
.\start_venv.bat
```

或：

```powershell
.\venv\Scripts\activate
```

2. 启动应用：

```powershell
python server.py
```

## 4. 访问应用

1. 打开浏览器，访问：`http://localhost:8000`
2. 您应该能看到 RealtimeVoiceChat 的用户界面

## 5. 使用指南

### 5.1 基本操作

- **开始对话**：点击麦克风图标开始录音
- **结束对话**：再次点击麦克风图标停止录音
- **重置对话**：点击重置按钮清除对话历史
- **调整语速**：使用语速滑块调整 AI 语音的播放速度

### 5.2 高级设置

您可以通过修改 `.env` 文件或在用户界面中更改以下设置：

- **LLM 提供商**：选择 Ollama、OpenAI 或 LMStudio
- **LLM 模型**：选择要使用的语言模型
- **TTS 引擎**：选择 Kokoro、Coqui 或 Orpheus
- **TTS 语音**：选择 TTS 语音选项

### 5.3 自定义系统提示词

您可以通过编辑 `system_prompt.txt` 文件自定义 AI 的行为和个性：

```powershell
notepad system_prompt.txt
```

修改后保存文件并重启应用以应用更改。

## 6. 故障排除

### 6.1 常见问题

#### Docker 相关问题

- **Docker 无法启动**：确保 WSL 2 已正确安装和配置
- **GPU 不可用**：检查 NVIDIA Container Toolkit 是否正确安装
- **端口冲突**：如果 8000 端口已被占用，修改 `docker-compose.yml` 中的端口映射

#### 手动安装问题

- **依赖安装失败**：尝试使用管理员权限运行命令提示符
- **CUDA 错误**：确保已安装兼容的 NVIDIA 驱动和 CUDA 版本
- **模型下载失败**：检查网络连接，或尝试使用代理

#### 运行时问题

- **麦克风不工作**：确保浏览器有麦克风访问权限
- **音频播放问题**：检查浏览器音频设置
- **高延迟**：如果使用 CPU 模式，考虑切换到 GPU 模式或使用更小的模型

### 6.2 日志查看

#### Docker 日志

```powershell
docker-compose logs -f
```

#### 应用日志

应用日志保存在 `logs` 目录中，可以查看以获取详细信息：

```powershell
type logs\app.log
```

### 6.3 资源监控

- 使用 Windows 任务管理器监控 CPU、GPU 和内存使用情况
- 对于 Docker 安装，可以使用 Docker Desktop 的资源监控功能

## 7. 更新应用

### 7.1 Docker 方式更新

```powershell
git pull
docker-compose down
docker-compose build --no-cache
docker-compose up
```

### 7.2 手动方式更新

```powershell
git pull
.\venv\Scripts\activate
pip install -r requirements.txt
python server.py
```

## 8. 卸载应用

### 8.1 Docker 方式卸载

```powershell
docker-compose down -v
```

### 8.2 手动方式卸载

只需删除项目目录即可。如果需要，也可以卸载 Python 和 CUDA。