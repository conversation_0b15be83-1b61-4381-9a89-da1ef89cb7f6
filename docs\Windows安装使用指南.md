# RealtimeVoiceChat Windows 10 Python 直接运行指南

## 1. 系统要求

### 1.1 硬件要求

- **处理器**：建议 Intel Core i5/AMD Ryzen 5 或更高
- **内存**：最低 8GB RAM，建议 16GB 或更高
- **存储**：至少 15GB 可用空间（包含模型文件）
- **GPU**：
  - 推荐：NVIDIA GPU（支持 CUDA）
  - 最低：GeForce GTX 1060 6GB 或同等性能
  - 理想：GeForce RTX 系列（更好的性能）
  - 注意：CPU 模式也可运行，但速度较慢

### 1.2 软件要求

- **操作系统**：Windows 10 64位（1909或更高版本）
- **Python**：Python 3.10（推荐 3.10.9，不支持 Python 3.13）
- **浏览器**：Chrome、Edge 或 Firefox 最新版本
- **CUDA**：如使用 GPU，需要 CUDA 12.1 或兼容版本
- **Git**：用于克隆项目（可选，也可下载ZIP）

## 2. Python 环境安装

### 2.1 安装 Python 3.10

1. 从 [Python 官网](https://www.python.org/downloads/release/python-3109/) 下载 Python 3.10.9
2. 运行安装程序，**重要**：勾选「Add Python to PATH」选项
3. 选择「Customize installation」进行自定义安装
4. 确保勾选「pip」和「py launcher」
5. 在「Advanced Options」中勾选「Add Python to environment variables」
6. 完成安装后，打开 PowerShell 验证安装：

```powershell
python --version  # 应显示 Python 3.10.9
pip --version     # 验证 pip 已安装
```

### 2.2 安装 CUDA（GPU 用户）

如果您有 NVIDIA GPU 并希望使用 GPU 加速：

1. 确保已安装最新的 [NVIDIA 显卡驱动](https://www.nvidia.com/download/index.aspx)
2. 下载并安装 [CUDA Toolkit 12.1](https://developer.nvidia.com/cuda-12-1-0-download-archive)
3. 安装完成后验证：

```powershell
nvcc --version  # 验证 CUDA 安装
nvidia-smi      # 查看 GPU 状态
```

## 3. 项目安装

### 3.1 获取项目代码

**方法一：使用 Git（推荐）**
```powershell
git clone https://github.com/yourusername/RealtimeVoiceChat.git
cd RealtimeVoiceChat
```

**方法二：下载 ZIP**
1. 从项目页面下载 ZIP 文件
2. 解压到您选择的目录
3. 在 PowerShell 中进入项目目录

### 3.2 创建虚拟环境

```powershell
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
.\venv\Scripts\activate

# 升级 pip
python -m pip install --upgrade pip
```

### 3.3 安装依赖

**GPU 用户（推荐）：**
```powershell
# 安装 PyTorch GPU 版本
pip install torch==2.1.0+cu121 torchaudio==2.1.0+cu121 --index-url https://download.pytorch.org/whl/cu121

# 安装项目依赖
pip install -r requirements.txt
```

**CPU 用户：**
```powershell
# 安装 PyTorch CPU 版本
pip install torch==2.1.0+cpu torchaudio==2.1.0+cpu --index-url https://download.pytorch.org/whl/cpu

# 安装项目依赖
pip install -r requirements.txt
```

## 4. 配置应用

### 4.1 创建配置文件

在项目根目录创建 `.env` 文件：

```powershell
# 复制示例配置文件（如果存在）
copy .env.example .env

# 或者创建新的配置文件
notepad .env
```

### 4.2 基本配置示例

在 `.env` 文件中添加以下配置：

```env
# 日志级别
LOG_LEVEL=INFO

# 音频队列大小
MAX_AUDIO_QUEUE_SIZE=50

# LLM 配置（如果使用第三方平台）
SILICONFLOW_API_KEY=your_api_key_here
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# OpenAI 配置（如果使用 OpenAI）
OPENAI_API_KEY=your_openai_api_key_here

# Ollama 配置（如果使用本地 Ollama）
OLLAMA_BASE_URL=http://127.0.0.1:11434

# LMStudio 配置（如果使用 LMStudio）
LMSTUDIO_BASE_URL=http://127.0.0.1:1234/v1
```

## 5. 启动应用

### 5.1 激活虚拟环境

每次运行前需要激活虚拟环境：

```powershell
# 进入项目目录
cd RealtimeVoiceChat

# 激活虚拟环境
.\venv\Scripts\activate
```

### 5.2 启动服务器

```powershell
# 进入代码目录
cd code

# 启动应用
python server.py
```

### 5.3 访问应用

1. 打开浏览器，访问：`http://localhost:8000`
2. 您应该能看到 RealtimeVoiceChat 的用户界面
3. 允许浏览器访问麦克风权限

## 6. 使用指南

### 6.1 基本操作

- **开始对话**：点击麦克风图标开始录音
- **结束对话**：再次点击麦克风图标停止录音
- **重置对话**：点击重置按钮清除对话历史
- **调整语速**：使用语速滑块调整 AI 语音的播放速度

### 6.2 自定义系统提示词

您可以通过编辑 `code/system_prompt.txt` 文件自定义 AI 的行为和个性：

```powershell
notepad code\system_prompt.txt
```

修改后保存文件并重启应用以应用更改。

## 7. 故障排除

### 7.1 常见问题

#### Python 环境问题

- **Python 版本错误**：确保使用 Python 3.10，不支持 3.13
- **虚拟环境激活失败**：确保在项目根目录运行激活命令
- **依赖安装失败**：尝试使用管理员权限运行 PowerShell
- **PyTorch 安装问题**：确保选择正确的 CUDA 版本或 CPU 版本

#### GPU 相关问题

- **CUDA 不可用**：检查 NVIDIA 驱动和 CUDA 版本兼容性
- **GPU 内存不足**：考虑使用更小的模型或切换到 CPU 模式
- **CUDA 版本不匹配**：确保 PyTorch 版本与 CUDA 版本兼容

#### 运行时问题

- **端口被占用**：如果 8000 端口被占用，修改 `server.py` 中的端口设置
- **麦克风不工作**：确保浏览器有麦克风访问权限
- **音频播放问题**：检查浏览器音频设置和系统音量
- **高延迟**：考虑使用 GPU 模式或更小的模型

#### 模型下载问题

- **网络连接问题**：检查网络连接，可能需要使用代理
- **磁盘空间不足**：确保有足够的磁盘空间存储模型文件
- **权限问题**：确保有写入模型目录的权限

### 7.2 日志查看

查看应用日志以诊断问题：

```powershell
# 查看控制台输出
# 应用运行时会在控制台显示详细日志

# 如果有日志文件
type logs\app.log
```

### 7.3 性能优化

- **GPU 用户**：确保使用 GPU 版本的 PyTorch
- **CPU 用户**：考虑使用更小的模型以提高响应速度
- **内存优化**：关闭不必要的应用程序释放内存

## 8. 更新应用

### 8.1 更新代码

```powershell
# 拉取最新代码
git pull

# 激活虚拟环境
.\venv\Scripts\activate

# 更新依赖
pip install -r requirements.txt --upgrade

# 重启应用
cd code
python server.py
```

### 8.2 更新模型

如果需要更新 AI 模型，请参考相应的模型配置文档。

## 9. 卸载应用

### 9.1 删除项目

```powershell
# 停止应用（Ctrl+C）
# 退出虚拟环境
deactivate

# 删除项目目录
cd ..
rmdir /s RealtimeVoiceChat
```

### 9.2 可选清理

如果不再需要 Python 环境：
- 可以卸载 Python 3.10
- 可以卸载 CUDA Toolkit（如果不用于其他项目）

## 10. 下一步

安装完成后，您可以：
1. 查看 [STT模型配置说明](./STT模型配置说明.md) 了解如何配置语音识别
2. 查看 [TTS模型配置说明](./TTS模型配置说明.md) 了解如何配置语音合成
3. 查看 [第三方LLM平台使用指南](./第三方LLM平台使用指南.md) 了解如何使用第三方 AI 平台