# custom_llm_provider.py
import logging
import os
import json
import uuid
from typing import Generator, List, Dict, Optional, Any
from threading import Lock

# --- Library Dependencies ---
try:
    import requests
    from requests import Session
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    logging.warning("🤖⚠️ requests library not installed. SiliconFlow backend will not function.")

# Configure logging
logger = logging.getLogger(__name__)

# --- Environment Variable Configuration ---
try:
    import importlib.util
    dotenv_spec = importlib.util.find_spec("dotenv")
    if dotenv_spec:
        from dotenv import load_dotenv
        load_dotenv()
        logger.debug("🤖⚙️ Loaded environment variables from .env file.")
    else:
        logger.debug("🤖⚙️ python-dotenv not installed, skipping .env load.")
except ImportError:
    logger.debug("🤖💥 Error importing dotenv, skipping .env load.")

SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY")
SILICONFLOW_BASE_URL = os.getenv("SILICONFLOW_BASE_URL", "https://api.siliconflow.cn/v1")

# --- Backend Client Creation/Check Functions ---
def _check_siliconflow_connection(base_url: str, session: Optional[Session], api_key: str) -> bool:
    """
    Performs a quick HTTP GET request to check connectivity with SiliconFlow API.

    Args:
        base_url: The base URL of the SiliconFlow API.
        session: An active requests.Session object to use for the check.
        api_key: The API key for authentication.

    Returns:
        True if the connection check is successful, False otherwise.
    """
    if not REQUESTS_AVAILABLE:
        logger.warning("🤖⚠️ Cannot check SiliconFlow connection: requests library not installed.")
        return False
    if not session:
        logger.warning("🤖⚠️ Cannot check SiliconFlow connection: requests session not provided.")
        return False
    try:
        check_endpoint = f"{base_url}/models"
        logger.debug(f"🤖🔌 Checking SiliconFlow connection via GET to {check_endpoint}...")
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        # Use a shorter timeout for the check
        response = session.get(check_endpoint, headers=headers, timeout=5.0)
        response.raise_for_status()
        logger.info(f"🤖🔌 Successfully connected to SiliconFlow API at: {base_url}")
        return True
    except requests.exceptions.ConnectionError as e:
        logger.warning(f"🤖🔌❌ Connection Error checking SiliconFlow at {base_url}: {e}")
        return False
    except requests.exceptions.Timeout:
        logger.warning(f"🤖🔌❌ Timeout checking SiliconFlow connection at {base_url}.")
        return False
    except requests.exceptions.RequestException as e:
        logger.warning(f"🤖🔌❌ Error checking SiliconFlow connection at {base_url}: {e}")
        return False
    except Exception as e:
        logger.error(f"🤖💥 Unexpected error during SiliconFlow connection check: {e}")
        return False

# --- SiliconFlow LLM Class ---
class SiliconFlowLLM:
    """
    Provides an interface for interacting with SiliconFlow API.

    Handles client initialization, streaming generation, request cancellation,
    system prompts, and basic connection management.
    """
    def __init__(
        self,
        model: str,
        system_prompt: Optional[str] = None,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        no_think: bool = False,
    ):
        """
        Initializes the SiliconFlow LLM interface.

        Args:
            model: The identifier for the specific model to use (e.g., "Qwen/QwQ-32B").
            system_prompt: An optional system prompt to prepend to conversations.
            api_key: API key for SiliconFlow API.
            base_url: Optional base URL for the API (overrides defaults/env vars).
            no_think: Flag to disable thinking mode in the API.

        Raises:
            ImportError: If required libraries are not installed.
        """
        logger.info(f"🤖⚙️ Initializing SiliconFlow LLM with model: {model}, system_prompt: {system_prompt}")

        if not REQUESTS_AVAILABLE:
            raise ImportError("requests library is required for the SiliconFlow backend but not installed.")

        self.model = model
        self.system_prompt = system_prompt
        self._api_key = api_key
        self._base_url = base_url
        self.no_think = no_think

        self.session: Optional[Session] = None
        self._client_initialized: bool = False
        self._client_init_lock = Lock()
        self._active_requests: Dict[str, Dict[str, Any]] = {}
        self._requests_lock = Lock()
        self._connection_ok: bool = False

        self.effective_api_key = self._api_key or SILICONFLOW_API_KEY
        self.effective_base_url = self._base_url or SILICONFLOW_BASE_URL

        if REQUESTS_AVAILABLE:
            self.session = requests.Session()
            logger.info("🤖🔌 Initialized requests.Session for SiliconFlow backend.")

        self.system_prompt_message = None
        if self.system_prompt:
            self.system_prompt_message = {"role": "system", "content": self.system_prompt}
            logger.info(f"🤖💬 System prompt set.")

    def _lazy_initialize_client(self) -> bool:
        """
        Initializes the client on first use (thread-safe).

        Returns:
            True if the client is initialized and ready, False otherwise.
        """
        if self._client_initialized:
            return self.session is not None and self._connection_ok

        with self._client_init_lock:
            if self._client_initialized:
                return self.session is not None and self._connection_ok

            logger.debug(f"🤖🔄 Lazy initializing/checking connection for SiliconFlow API")
            init_ok = False
            self._connection_ok = False

            try:
                if self.session and self.effective_base_url and self.effective_api_key:
                    init_ok = _check_siliconflow_connection(
                        self.effective_base_url,
                        self.session,
                        self.effective_api_key
                    )
                    self._connection_ok = init_ok
                else:
                    logger.error("🤖💥 Session object is None or URL/API key not set during lazy init.")
                    init_ok = False

                if init_ok:
                    logger.info(f"🤖✅ Connection initialized successfully for SiliconFlow API.")
                else:
                    logger.error(f"🤖💥 Initialization failed for SiliconFlow API.")
            except Exception as e:
                logger.exception(f"🤖💥 Critical failure during lazy initialization for SiliconFlow API: {e}")
                init_ok = False
            finally:
                self._client_initialized = True
                if not init_ok:
                    self._connection_ok = False

            return init_ok

    def cancel_generation(self, request_id: Optional[str] = None) -> bool:
        """
        Requests cancellation of active generation streams.

        Args:
            request_id: The unique ID of the generation request to cancel, or None to cancel all.

        Returns:
            True if at least one request cancellation was attempted, False otherwise.
        """
        cancelled_any = False
        with self._requests_lock:
            ids_to_cancel = []
            if request_id is None:
                if not self._active_requests:
                    logger.debug("🤖🗑️ Cancel all requested, but no active requests found.")
                    return False
                logger.info(f"🤖🗑️ Attempting to cancel ALL active generation requests ({len(self._active_requests)}).")
                ids_to_cancel = list(self._active_requests.keys())
            else:
                if request_id not in self._active_requests:
                    logger.warning(f"🤖🗑️ Cancel requested for ID '{request_id}', but it's not an active request.")
                    return False
                logger.info(f"🤖🗑️ Attempting to cancel generation request: {request_id}")
                ids_to_cancel.append(request_id)

            for req_id in ids_to_cancel:
                if self._cancel_single_request_unsafe(req_id):
                    cancelled_any = True
        return cancelled_any

    def _cancel_single_request_unsafe(self, request_id: str) -> bool:
        """
        Internal helper to handle cancellation for a single request (thread-unsafe).

        Args:
            request_id: The unique ID of the request to cancel.

        Returns:
            True if the request was found and removal/close attempt was made, False otherwise.
        """
        if request_id not in self._active_requests:
            return False

        request_data = self._active_requests.pop(request_id)
        logger.debug(f"🤖🗑️ Removed request {request_id} from tracking.")

        stream_obj = request_data.get("stream_object")
        if stream_obj and hasattr(stream_obj, "close"):
            try:
                logger.debug(f"🤖🗑️ Attempting to close stream for request {request_id}.")
                stream_obj.close()
                logger.debug(f"🤖🗑️ Successfully closed stream for request {request_id}.")
            except Exception as e:
                logger.warning(f"🤖⚠️ Error closing stream for request {request_id}: {e}")

        return True

    def _register_request(self, request_id: str, stream_object: Any) -> None:
        """
        Registers an active generation stream for tracking and potential cancellation.

        Args:
            request_id: A unique identifier for this generation request.
            stream_object: The stream object that should be closed on cancellation.
        """
        with self._requests_lock:
            self._active_requests[request_id] = {
                "id": request_id,
                "stream_object": stream_object,
                "timestamp": uuid.uuid1().time_low  # Simple timestamp for age tracking
            }
            logger.debug(f"🤖📝 Registered request {request_id} for tracking. Active requests: {len(self._active_requests)}")

    def generate(
        self,
        text: str,
        history: Optional[List[Dict[str, str]]] = None,
        use_system_prompt: bool = True,
        request_id: Optional[str] = None,
        **kwargs: Any
    ) -> Generator[str, None, None]:
        """
        Generates text using SiliconFlow API, yielding tokens as a stream.

        Args:
            text: The user's input prompt/text.
            history: An optional list of previous messages (dicts with "role" and "content").
            use_system_prompt: If True, prepends the configured system prompt (if any).
            request_id: An optional unique ID for this generation request. If None, one is generated.
            **kwargs: Additional keyword arguments (e.g., temperature, top_p, etc.).

        Yields:
            str: Individual tokens (or small chunks of text) as they are generated.

        Raises:
            RuntimeError: If the client fails to initialize.
            ConnectionError: If communication with the API fails.
            Exception: For other unexpected errors during the generation process.
        """
        if not self._lazy_initialize_client():
            raise RuntimeError("SiliconFlow API client failed to initialize.")

        req_id = request_id if request_id else f"siliconflow-{uuid.uuid4()}"
        logger.info(f"🤖💬 Starting generation (Request ID: {req_id})")

        messages = []
        if use_system_prompt and self.system_prompt_message:
            messages.append(self.system_prompt_message)
        if history:
            messages.extend(history)

        if len(messages) == 0 or messages[-1]["role"] != "user":
            messages.append({"role": "user", "content": text})
        logger.debug(f"🤖💬 [{req_id}] Prepared messages count: {len(messages)}")

        stream_response = None

        try:
            # Prepare API request
            api_url = f"{self.effective_base_url}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.effective_api_key}",
                "Content-Type": "application/json"
            }

            # Set default parameters if not provided
            if 'temperature' not in kwargs:
                kwargs['temperature'] = 0.7
            if 'top_p' not in kwargs:
                kwargs['top_p'] = 0.7
            if 'max_tokens' not in kwargs:
                kwargs['max_tokens'] = 512

            payload = {
                "model": self.model,
                "messages": messages,
                "stream": True,  # Always use streaming mode
                "enable_thinking": not self.no_think,
                **kwargs
            }

            logger.info(f"🤖💬 [{req_id}] Sending SiliconFlow request with payload:")
            logger.info(f"{json.dumps(payload, indent=2)}")

            # Make the API request with streaming enabled
            stream_response = self.session.post(
                api_url,
                headers=headers,
                json=payload,
                stream=True,
                timeout=(10.0, 600.0)  # (connect_timeout, read_timeout)
            )
            stream_response.raise_for_status()

            # Register the stream for potential cancellation
            self._register_request(req_id, stream_response)

            # Process the streaming response
            for line in stream_response.iter_lines():
                # Check for cancellation before processing each chunk
                with self._requests_lock:
                    if req_id not in self._active_requests:
                        logger.info(f"🤖🗑️ SiliconFlow stream {req_id} cancelled or finished externally during iteration.")
                        break

                if not line:
                    continue

                # Decode line to string if it's bytes
                if isinstance(line, bytes):
                    line = line.decode('utf-8')

                if line.startswith('data: '):
                    data = line[6:]  # Remove 'data: ' prefix
                    if data.strip() == '[DONE]':
                        break

                    try:
                        chunk = json.loads(data)
                        if 'choices' in chunk and len(chunk['choices']) > 0:
                            choice = chunk['choices'][0]
                            # Handle both delta and message formats
                            if 'delta' in choice and 'content' in choice['delta']:
                                content = choice['delta']['content']
                                if content:
                                    yield content
                            elif 'message' in choice and 'content' in choice['message']:
                                content = choice['message']['content']
                                if content:
                                    yield content
                    except json.JSONDecodeError as e:
                        logger.warning(f"🤖⚠️ JSON decode error in SiliconFlow stream: {e}")
                    except Exception as e:
                        logger.error(f"🤖💥 Error processing SiliconFlow stream chunk: {e}")

            logger.info(f"🤖✅ Finished generating stream successfully (request_id: {req_id})")

        except requests.exceptions.ConnectionError as e:
            logger.error(f"🤖💥 Connection Error during generation for {req_id}: {e}", exc_info=False)
            raise ConnectionError(f"Communication error during generation: {e}") from e
        except requests.exceptions.RequestException as e:
            logger.error(f"🤖💥 Request Error during generation for {req_id}: {e}", exc_info=False)
            raise
        except Exception as e:
            logger.error(f"🤖💥 Unexpected error in generation pipeline for {req_id}: {e}", exc_info=True)
            raise
        finally:
            # Clean up the request tracking
            with self._requests_lock:
                if req_id in self._active_requests:
                    logger.debug(f"🤖🗑️ [{req_id}] Removing request from tracking in generate's finally block.")
                    self._cancel_single_request_unsafe(req_id)
                else:
                    logger.debug(f"🤖🗑️ [{req_id}] Request already removed from tracking before finally block completion.")

    def prewarm(self, timeout: float = 10.0) -> bool:
        """
        Prewarms the LLM connection by checking API connectivity.

        Args:
            timeout: Maximum time in seconds to wait for the connection check (currently unused).

        Returns:
            True if the connection is ready, False otherwise.
        """
        try:
            logger.info("🤖🔥 Prewarming SiliconFlow API connection...")
            # Note: timeout parameter is currently not used in the implementation
            return self._lazy_initialize_client()
        except Exception as e:
            logger.error(f"🤖💥 Error during prewarm: {e}")
            return False

    def measure_inference_time(self, test_prompt: str = "Hello") -> float:
        """
        Measures the inference time by making a simple API call.

        Args:
            test_prompt: The test prompt to use for measurement.

        Returns:
            The inference time in milliseconds.
        """
        try:
            import time
            start_time = time.time()

            # Make a simple generation request
            generator = self.generate(
                text=test_prompt,
                history=None,
                use_system_prompt=False,
                max_tokens=10,
                temperature=0.1
            )

            # Consume the first token to measure TTFT
            _ = next(generator, None)  # We don't need to use the token value

            end_time = time.time()
            inference_time_ms = (end_time - start_time) * 1000

            logger.info(f"🤖⏱️ SiliconFlow inference time measured: {inference_time_ms:.2f}ms")
            return inference_time_ms

        except Exception as e:
            logger.warning(f"🤖⚠️ Failed to measure inference time: {e}")
            return 1000.0  # Return default 1 second if measurement fails