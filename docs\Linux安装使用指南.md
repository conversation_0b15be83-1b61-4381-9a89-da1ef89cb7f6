# RealtimeVoiceChat Linux 安装使用指南

## 1. 系统要求

### 1.1 硬件要求

- **处理器**：建议 Intel Core i5/AMD Ryzen 5 或更高
- **内存**：最低 8GB RAM，建议 16GB 或更高
- **存储**：至少 10GB 可用空间
- **GPU**：
  - 推荐：NVIDIA GPU（支持 CUDA）
  - 最低：GeForce GTX 1060 6GB 或同等性能
  - 理想：GeForce RTX 系列（更好的性能）

### 1.2 软件要求

- **操作系统**：Ubuntu 20.04/22.04 LTS、Debian 11/12 或其他主流 Linux 发行版
- **Python**：Python 3.10（不支持 Python 3.13）
- **浏览器**：Chrome、Firefox 或 Edge 最新版本
- **CUDA**：如使用 GPU，需要 CUDA 12.1（Docker 方式会自动安装）

## 2. 安装方法

### 2.1 使用 Docker 安装（推荐）

使用 Docker 是最简单的安装方法，它会自动处理所有依赖项。

#### 2.1.1 安装 Docker 和 Docker Compose

**Ubuntu/Debian**:

```bash
# 安装 Docker
sudo apt update
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.23.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将当前用户添加到 docker 组（避免每次使用 sudo）
sudo usermod -aG docker $USER
```

安装后，注销并重新登录以应用组更改。

#### 2.1.2 安装 NVIDIA Container Toolkit（如使用 NVIDIA GPU）

```bash
# 添加 NVIDIA 软件包仓库
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

# 安装 NVIDIA Container Toolkit
sudo apt update
sudo apt install -y nvidia-container-toolkit

# 重启 Docker 服务
sudo systemctl restart docker
```

#### 2.1.3 克隆项目

```bash
git clone https://github.com/yourusername/RealtimeVoiceChat.git
cd RealtimeVoiceChat
```

如果没有 Git，可以使用以下命令安装：

```bash
sudo apt install -y git
```

#### 2.1.4 配置环境变量

创建 `.env` 文件（或复制 `.env.example` 并重命名为 `.env`），设置以下变量：

```bash
# 创建 .env 文件
cp .env.example .env  # 如果存在示例文件
# 或者手动创建
touch .env
nano .env  # 或使用您喜欢的文本编辑器
```

添加以下内容：

```
# 必需设置
OPENAI_API_KEY=your_openai_api_key_here  # 如果使用 OpenAI API

# 可选设置
DEFAULT_LLM_PROVIDER=ollama  # ollama, openai, lmstudio
DEFAULT_LLM_MODEL=llama3     # 模型名称
DEFAULT_TTS_ENGINE=kokoro    # kokoro, coqui, orpheus
DEFAULT_TTS_VOICE=v2/en_US/cori/medium  # TTS 语音
```

#### 2.1.5 启动 Docker 容器

```bash
docker-compose up
```

首次启动时，Docker 会下载和构建所需的镜像，这可能需要一些时间。

### 2.2 手动安装

如果您不想使用 Docker，也可以手动安装。

#### 2.2.1 安装系统依赖

**Ubuntu/Debian**:

```bash
sudo apt update
sudo apt install -y python3.10 python3.10-venv python3.10-dev python3-pip build-essential libsndfile1 ffmpeg
```

**Fedora**:

```bash
sudo dnf install -y python3.10 python3.10-devel python3-pip gcc-c++ libsndfile ffmpeg
```

**Arch Linux**:

```bash
sudo pacman -Sy python python-pip base-devel libsndfile ffmpeg
```

#### 2.2.2 安装 CUDA（如使用 NVIDIA GPU）

**Ubuntu/Debian**:

```bash
# 添加 NVIDIA 软件包仓库
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.1-1_all.deb
sudo dpkg -i cuda-keyring_1.1-1_all.deb
sudo apt update

# 安装 CUDA 12.1
sudo apt install -y cuda-12-1

# 设置环境变量
echo 'export PATH=/usr/local/cuda-12.1/bin:$PATH' >> ~/.bashrc
echo 'export LD_LIBRARY_PATH=/usr/local/cuda-12.1/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
source ~/.bashrc
```

#### 2.2.3 克隆项目

```bash
git clone https://github.com/yourusername/RealtimeVoiceChat.git
cd RealtimeVoiceChat
```

#### 2.2.4 创建虚拟环境并安装依赖

```bash
# 创建虚拟环境
python3.10 -m venv venv
source venv/bin/activate

# 升级 pip
pip install --upgrade pip

# 安装 PyTorch（带 CUDA 支持）
pip install torch==2.1.0+cu121 -f https://download.pytorch.org/whl/torch_stable.html

# 安装其他依赖
pip install -r requirements.txt
```

#### 2.2.5 配置环境变量

创建 `.env` 文件，设置与 Docker 安装相同的环境变量。

#### 2.2.6 安装 Ollama（如使用 Ollama 作为 LLM 提供商）

```bash
curl -fsSL https://ollama.com/install.sh | sh

# 拉取所需模型
ollama pull llama3
```

## 3. 启动应用

### 3.1 Docker 方式启动

如果您使用 Docker 安装，应用已经在运行。如果需要重新启动：

```bash
docker-compose up
```

如果要在后台运行：

```bash
docker-compose up -d
```

### 3.2 手动方式启动

1. 激活虚拟环境（如果尚未激活）：

```bash
source venv/bin/activate
```

2. 启动应用：

```bash
python server.py
```

如果要在后台运行：

```bash
nohup python server.py > app.log 2>&1 &
```

## 4. 访问应用

1. 打开浏览器，访问：`http://localhost:8000`
2. 如果在远程服务器上运行，请使用服务器的 IP 地址：`http://<服务器IP>:8000`

## 5. 使用指南

### 5.1 基本操作

- **开始对话**：点击麦克风图标开始录音
- **结束对话**：再次点击麦克风图标停止录音
- **重置对话**：点击重置按钮清除对话历史
- **调整语速**：使用语速滑块调整 AI 语音的播放速度

### 5.2 高级设置

您可以通过修改 `.env` 文件或在用户界面中更改以下设置：

- **LLM 提供商**：选择 Ollama、OpenAI 或 LMStudio
- **LLM 模型**：选择要使用的语言模型
- **TTS 引擎**：选择 Kokoro、Coqui 或 Orpheus
- **TTS 语音**：选择 TTS 语音选项

### 5.3 自定义系统提示词

您可以通过编辑 `system_prompt.txt` 文件自定义 AI 的行为和个性：

```bash
nano system_prompt.txt
```

修改后保存文件并重启应用以应用更改。

## 6. 故障排除

### 6.1 常见问题

#### Docker 相关问题

- **权限错误**：确保当前用户已添加到 docker 组
- **GPU 不可用**：检查 NVIDIA Container Toolkit 是否正确安装
  ```bash
  # 验证 NVIDIA Docker 是否正常工作
  docker run --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi
  ```
- **端口冲突**：如果 8000 端口已被占用，修改 `docker-compose.yml` 中的端口映射

#### 手动安装问题

- **依赖安装失败**：检查是否安装了所有必要的系统依赖
- **CUDA 错误**：确保已安装兼容的 NVIDIA 驱动和 CUDA 版本
  ```bash
  # 检查 NVIDIA 驱动和 CUDA 版本
  nvidia-smi
  ```
- **模型下载失败**：检查网络连接，或尝试使用代理

#### 运行时问题

- **麦克风不工作**：确保浏览器有麦克风访问权限
- **音频播放问题**：检查浏览器音频设置
- **高延迟**：如果使用 CPU 模式，考虑切换到 GPU 模式或使用更小的模型

### 6.2 日志查看

#### Docker 日志

```bash
docker-compose logs -f
```

#### 应用日志

应用日志保存在 `logs` 目录中，可以查看以获取详细信息：

```bash
cat logs/app.log
```

如果使用 `nohup` 运行，可以查看 `app.log` 文件：

```bash
cat app.log
```

### 6.3 资源监控

- 使用 `htop` 或 `top` 监控 CPU 和内存使用情况：
  ```bash
htop
  ```
- 使用 `nvidia-smi` 监控 GPU 使用情况：
  ```bash
watch -n 1 nvidia-smi
  ```

## 7. 更新应用

### 7.1 Docker 方式更新

```bash
git pull
docker-compose down
docker-compose build --no-cache
docker-compose up
```

### 7.2 手动方式更新

```bash
git pull
source venv/bin/activate
pip install -r requirements.txt
python server.py
```

## 8. 卸载应用

### 8.1 Docker 方式卸载

```bash
docker-compose down -v
```

### 8.2 手动方式卸载

只需删除项目目录即可：

```bash
cd ..
rm -rf RealtimeVoiceChat
```

如果需要，也可以卸载 Python 和 CUDA。

## 9. 防火墙配置

如果您在服务器上运行应用，可能需要配置防火墙以允许访问 8000 端口：

**Ubuntu/Debian (UFW)**:

```bash
sudo ufw allow 8000/tcp
sudo ufw reload
```

**CentOS/RHEL (firewalld)**:

```bash
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

## 10. 系统服务配置（可选）

如果您想将应用设置为系统服务，以便在系统启动时自动启动，可以创建一个 systemd 服务：

```bash
sudo nano /etc/systemd/system/voicechat.service
```

添加以下内容（根据您的安装路径进行调整）：

```
[Unit]
Description=RealtimeVoiceChat Service
After=network.target

[Service]
User=your_username
WorkingDirectory=/path/to/RealtimeVoiceChat
ExecStart=/path/to/RealtimeVoiceChat/venv/bin/python server.py
Restart=on-failure

[Install]
WantedBy=multi-user.target
```

启用并启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable voicechat.service
sudo systemctl start voicechat.service
```

检查服务状态：

```bash
sudo systemctl status voicechat.service
```