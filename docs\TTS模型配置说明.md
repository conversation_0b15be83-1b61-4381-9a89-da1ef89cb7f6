# TTS（文本转语音）模型配置说明

## 1. 概述

RealtimeVoiceChat 支持三种 TTS 引擎：Coqui、Kokoro 和 Orpheus。每种引擎都有不同的特点和配置选项。本文档详细说明如何配置和切换不同的 TTS 模型。

## 2. 支持的 TTS 引擎

### 2.1 引擎对比

| 引擎 | 特点 | 语音质量 | 速度 | 资源需求 | 语言支持 |
|------|------|----------|------|----------|----------|
| **Coqui** | 高质量克隆语音 | 很高 | 中等 | 高 | 多语言 |
| **Kokoro** | 快速自然语音 | 高 | 快 | 中等 | 主要英语 |
| **Orpheus** | 大模型语音合成 | 最高 | 慢 | 最高 | 多语言 |

### 2.2 引擎详细介绍

#### 2.2.1 Coqui Engine
- **优点**：支持语音克隆，质量很高，支持多语言
- **缺点**：需要较多 GPU 内存，初始化较慢
- **适用场景**：需要特定语音风格或语音克隆的应用

#### 2.2.2 Kokoro Engine  
- **优点**：速度快，自然度高，资源需求适中
- **缺点**：主要支持英语，语音选择有限
- **适用场景**：英语对话，需要快速响应的应用

#### 2.2.3 Orpheus Engine
- **优点**：基于大模型，语音质量最高，表达力强
- **缺点**：资源需求最高，速度最慢
- **适用场景**：对语音质量要求极高的应用

## 3. 配置方法

### 3.1 切换 TTS 引擎

在 `code/server.py` 文件中修改以下配置：

```python
# 选择 TTS 引擎（三选一）
TTS_START_ENGINE = "coqui"    # Coqui 引擎
# TTS_START_ENGINE = "kokoro"   # Kokoro 引擎  
# TTS_START_ENGINE = "orpheus"  # Orpheus 引擎

# Orpheus 模型路径（仅在使用 Orpheus 时需要）
TTS_ORPHEUS_MODEL = "orpheus-3b-0.1-ft-Q8_0-GGUF/orpheus-3b-0.1-ft-q8_0.gguf"
```

### 3.2 引擎特定配置

TTS 引擎的详细配置位于 `code/audio_module.py` 文件的 `AudioProcessor.__init__` 方法中。

## 4. Coqui 引擎配置

### 4.1 基本配置

```python
if engine == "coqui":
    self.engine = CoquiEngine(
        specific_model="Lasinya",           # 模型名称
        local_models_path="./models",      # 模型存储路径
        voice="reference_audio.wav",       # 参考语音文件
        speed=1.1,                         # 语音速度
        use_deepspeed=True,                # 使用 DeepSpeed 加速
        thread_count=6,                    # 线程数
        stream_chunk_size=self.current_stream_chunk_size,
        overlap_wav_len=1024,              # 音频重叠长度
        load_balancing=True,               # 负载均衡
        load_balancing_buffer_length=0.5,  # 缓冲区长度
        load_balancing_cut_off=0.1,        # 切断阈值
        add_sentence_filter=True,          # 句子过滤
    )
```

### 4.2 语音克隆配置

要使用自定义语音，需要提供参考音频文件：

1. 准备参考音频文件（WAV 格式，建议 10-30 秒）
2. 将文件放在 `code/` 目录下
3. 修改配置：

```python
voice="your_reference_audio.wav",  # 您的参考音频文件
```

### 4.3 性能优化配置

```python
# GPU 优化配置
use_deepspeed=True,        # 启用 DeepSpeed（需要 GPU）
thread_count=8,            # 增加线程数（根据 CPU 核心数调整）
load_balancing=True,       # 启用负载均衡

# 质量 vs 速度调整
speed=1.2,                 # 提高语音速度
stream_chunk_size=2048,    # 增大块大小（可能影响延迟）
```

## 5. Kokoro 引擎配置

### 5.1 基本配置

```python
if engine == "kokoro":
    self.engine = KokoroEngine(
        voice="af_heart",              # 语音类型
        default_speed=1.26,            # 默认语音速度
        trim_silence=True,             # 修剪静音
        silence_threshold=0.01,        # 静音阈值
        extra_start_ms=25,             # 开始额外时间
        extra_end_ms=15,               # 结束额外时间
        fade_in_ms=15,                 # 淡入时间
        fade_out_ms=10,                # 淡出时间
    )
```

### 5.2 可用语音类型

Kokoro 支持以下语音类型：

```python
# 可选的语音类型
voice="af_heart"      # 默认女声
voice="af_sky"        # 另一种女声
voice="am_adam"       # 男声
voice="am_michael"    # 另一种男声
```

### 5.3 语音质量调整

```python
# 高质量配置
default_speed=1.0,             # 较慢的语音速度
silence_threshold=0.005,       # 更严格的静音检测
extra_start_ms=50,             # 更长的开始时间
extra_end_ms=30,               # 更长的结束时间
fade_in_ms=25,                 # 更长的淡入
fade_out_ms=20,                # 更长的淡出

# 快速配置
default_speed=1.5,             # 更快的语音速度
silence_threshold=0.02,        # 宽松的静音检测
extra_start_ms=10,             # 更短的开始时间
extra_end_ms=5,                # 更短的结束时间
```

## 6. Orpheus 引擎配置

### 6.1 基本配置

```python
if engine == "orpheus":
    self.engine = OrpheusEngine(
        model=self.orpheus_model,      # 模型路径
        temperature=0.8,               # 生成温度
        top_p=0.95,                    # Top-p 采样
        repetition_penalty=1.1,        # 重复惩罚
        max_tokens=1200,               # 最大 token 数
    )
    voice = OrpheusVoice("tara")       # 语音角色
    self.engine.set_voice(voice)
```

### 6.2 模型选择

Orpheus 支持不同大小的模型：

```python
# 小模型（较快）
TTS_ORPHEUS_MODEL = "orpheus-1b-Q4_K_M.gguf"

# 中等模型（平衡）
TTS_ORPHEUS_MODEL = "orpheus-3b-0.1-ft-Q8_0-GGUF/orpheus-3b-0.1-ft-q8_0.gguf"

# 大模型（最高质量）
TTS_ORPHEUS_MODEL = "Orpheus_3B-1BaseGGUF/mOrpheus_3B-1Base_Q4_K_M.gguf"
```

### 6.3 生成参数调整

```python
# 高质量配置
temperature=0.6,               # 较低温度，更稳定
top_p=0.9,                     # 较严格的采样
repetition_penalty=1.2,        # 更强的重复惩罚
max_tokens=1500,               # 更多 tokens

# 快速配置
temperature=1.0,               # 较高温度，更快生成
top_p=0.95,                    # 标准采样
repetition_penalty=1.0,        # 较弱的重复惩罚
max_tokens=800,                # 较少 tokens
```

### 6.4 可用语音角色

```python
# 可选的语音角色
voice = OrpheusVoice("tara")      # 女声
voice = OrpheusVoice("alex")      # 男声
voice = OrpheusVoice("sarah")     # 另一种女声
```

## 7. 静音配置

每个引擎都有对应的静音配置，位于 `ENGINE_SILENCES` 字典中：

```python
ENGINE_SILENCES = {
    "coqui":   Silence(comma=0.3, sentence=0.6, default=0.3),
    "kokoro":  Silence(comma=0.3, sentence=0.6, default=0.3),
    "orpheus": Silence(comma=0.3, sentence=0.6, default=0.3),
}
```

可以调整这些值来改变语音的节奏：

```python
# 更快的语音节奏
"coqui": Silence(comma=0.1, sentence=0.3, default=0.1),

# 更慢的语音节奏  
"coqui": Silence(comma=0.5, sentence=1.0, default=0.5),
```

## 8. 应用配置更改

### 8.1 修改配置

1. 编辑 `code/server.py` 选择 TTS 引擎
2. 编辑 `code/audio_module.py` 调整引擎参数
3. 保存文件

### 8.2 重启应用

```powershell
# 停止当前应用（Ctrl+C）
# 重新启动
cd code
python server.py
```

### 8.3 验证配置

启动时查看控制台输出：

```
🖥️⚙️ [PARAM] Starting engine: kokoro
🔊⚙️ Initializing AudioProcessor with engine: kokoro
🔊✅ TTS engine initialized successfully
```

## 9. 性能优化建议

### 9.1 硬件配置建议

**CPU 用户：**
- 推荐：Kokoro 引擎
- 备选：Coqui（禁用 DeepSpeed）

**GPU 用户：**
- 推荐：Coqui 或 Orpheus
- 高端 GPU：Orpheus 大模型

**内存有限：**
- 推荐：Kokoro 引擎
- 或使用 Orpheus 小模型

### 9.2 延迟优化

```python
# 减少延迟的配置
stream_chunk_size=1024,        # 较小的块大小
load_balancing_buffer_length=0.2,  # 较小的缓冲区
extra_start_ms=10,             # 较短的开始时间
```

### 9.3 质量优化

```python
# 提高质量的配置
use_deepspeed=True,            # 启用 DeepSpeed
thread_count=8,                # 增加线程数
temperature=0.6,               # 较低的温度（Orpheus）
```

## 10. 故障排除

### 10.1 常见问题

**模型加载失败：**
- 检查模型文件路径
- 确保有足够的磁盘空间和内存
- 验证模型文件完整性

**音质问题：**
- 尝试不同的引擎
- 调整生成参数
- 检查参考音频质量（Coqui）

**性能问题：**
- 使用更轻量的引擎或模型
- 调整线程数和缓冲区大小
- 监控系统资源使用

### 10.2 调试技巧

启用详细日志查看 TTS 处理过程：

```python
# 在 audio_module.py 中添加
import logging
logging.getLogger("RealtimeTTS").setLevel(logging.DEBUG)
```

这将显示详细的 TTS 处理信息，帮助诊断问题。
