# RealtimeVoiceChat 中文文档

欢迎使用 RealtimeVoiceChat！这是一个实时语音对话系统，支持语音识别、大语言模型对话和语音合成。

## 📚 文档目录

### 🚀 快速开始

1. **[Windows 10 Python 直接运行指南](./Windows安装使用指南.md)**
   - 系统要求和环境准备
   - Python 环境安装
   - 项目安装和配置
   - 启动和使用说明
   - 故障排除

### 🎤 语音识别 (STT) 配置

2. **[STT模型配置说明](./STT模型配置说明.md)**
   - 支持的 Whisper 模型
   - 模型选择建议
   - 配置参数详解
   - 性能优化技巧
   - 多语言配置

### 🔊 语音合成 (TTS) 配置

3. **[TTS模型配置说明](./TTS模型配置说明.md)**
   - 三种 TTS 引擎对比
   - Coqui、Kokoro、Orpheus 配置
   - 语音克隆设置
   - 性能调优建议

### 🤖 大语言模型 (LLM) 配置

4. **[第三方LLM平台使用指南](./第三方LLM平台使用指南.md)**
   - SiliconFlow 平台集成
   - 其他第三方平台配置
   - API 参数调优
   - 成本优化建议

## 🎯 快速启动

### 方法一：使用启动脚本（推荐）

```powershell
# Windows 用户
.\start_app.bat

# 或使用 Python 脚本
python start_app.py
```

### 方法二：手动启动

```powershell
# 1. 激活虚拟环境
.\venv\Scripts\activate

# 2. 进入代码目录
cd code

# 3. 启动服务器
python server.py
```

## 🔧 配置概览

### LLM 提供商选择

| 提供商 | 特点 | 配置方式 |
|--------|------|----------|
| **SiliconFlow** | 第三方平台，成本低 | 设置 API 密钥 |
| **Ollama** | 本地运行，隐私好 | 安装 Ollama 和模型 |
| **OpenAI** | 官方 API，质量高 | 设置 OpenAI API 密钥 |
| **LMStudio** | 本地 API 服务 | 启动 LMStudio 服务 |

### TTS 引擎选择

| 引擎 | 特点 | 适用场景 |
|------|------|----------|
| **Kokoro** | 快速，英语 | 英语对话，快速响应 |
| **Coqui** | 高质量，多语言 | 语音克隆，多语言 |
| **Orpheus** | 最高质量，慢 | 高质量要求 |

### STT 模型选择

| 模型 | 大小 | 速度 | 准确性 | 推荐用途 |
|------|------|------|--------|----------|
| `tiny.en` | 39MB | 最快 | 较低 | 快速测试 |
| `base.en` | 74MB | 快 | 中等 | 一般使用 |
| `small.en` | 244MB | 中等 | 好 | 平衡性能 |
| `medium.en` | 769MB | 慢 | 很好 | 高质量识别 |

## 🌟 功能特性

- **实时语音识别**：基于 Whisper 模型的高精度语音转文本
- **智能对话**：支持多种大语言模型，包括本地和云端
- **自然语音合成**：多种 TTS 引擎，支持语音克隆
- **低延迟设计**：优化的流式处理，实现实时对话体验
- **多语言支持**：支持中文、英文等多种语言
- **灵活配置**：可自定义模型、参数和提示词

## 📋 系统要求

### 最低要求
- Windows 10 64位
- Python 3.10
- 8GB RAM
- 10GB 可用磁盘空间

### 推荐配置
- Windows 10/11 64位
- Python 3.10
- 16GB+ RAM
- NVIDIA GPU (GTX 1060 或更高)
- 15GB+ 可用磁盘空间

## 🔗 相关链接

- **项目主页**：[GitHub Repository](https://github.com/yourusername/RealtimeVoiceChat)
- **问题反馈**：[Issues](https://github.com/yourusername/RealtimeVoiceChat/issues)
- **讨论交流**：[Discussions](https://github.com/yourusername/RealtimeVoiceChat/discussions)

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查看文档**：首先查看相关的配置文档
2. **检查日志**：查看控制台输出的错误信息
3. **搜索问题**：在 GitHub Issues 中搜索类似问题
4. **提交问题**：如果问题未解决，请提交新的 Issue

## 🤝 贡献指南

欢迎贡献代码和文档！请：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 [MIT 许可证](../LICENSE)。

## 🙏 致谢

感谢以下开源项目：

- [RealtimeSTT](https://github.com/KoljaB/RealtimeSTT) - 实时语音识别
- [RealtimeTTS](https://github.com/KoljaB/RealtimeTTS) - 实时语音合成
- [FastAPI](https://fastapi.tiangolo.com/) - Web 框架
- [Whisper](https://github.com/openai/whisper) - 语音识别模型

---

**开始您的实时语音对话之旅吧！** 🎉
