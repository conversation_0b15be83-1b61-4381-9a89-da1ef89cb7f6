#!/usr/bin/env python3
"""
RealtimeVoiceChat 启动脚本

这个脚本帮助用户更容易地启动 RealtimeVoiceChat 应用。
它会检查环境、激活虚拟环境并启动服务器。
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """打印应用横幅"""
    print("=" * 60)
    print("🎤 RealtimeVoiceChat 启动器")
    print("=" * 60)
    print()

def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    print(f"🐍 Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor != 10:
        print("⚠️  警告: 推荐使用 Python 3.10")
        if version.major != 3 or version.minor < 8:
            print("❌ 错误: 需要 Python 3.8 或更高版本")
            return False
    else:
        print("✅ Python 版本符合要求")
    
    return True

def check_virtual_environment():
    """检查虚拟环境"""
    venv_path = Path("venv")
    
    if not venv_path.exists():
        print("❌ 虚拟环境不存在")
        print("请先运行以下命令创建虚拟环境:")
        print("  python -m venv venv")
        print("  .\\venv\\Scripts\\activate  (Windows)")
        print("  source venv/bin/activate  (Linux/Mac)")
        print("  pip install -r requirements.txt")
        return False
    
    print("✅ 虚拟环境存在")
    return True

def check_dependencies():
    """检查关键依赖"""
    try:
        import torch
        print(f"✅ PyTorch 已安装: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA 可用: {torch.version.cuda}")
            print(f"   GPU 数量: {torch.cuda.device_count()}")
        else:
            print("⚠️  CUDA 不可用，将使用 CPU 模式")
            
    except ImportError:
        print("❌ PyTorch 未安装")
        return False
    
    try:
        import fastapi
        print(f"✅ FastAPI 已安装: {fastapi.__version__}")
    except ImportError:
        print("❌ FastAPI 未安装")
        return False
    
    return True

def check_config_files():
    """检查配置文件"""
    code_path = Path("code")
    if not code_path.exists():
        print("❌ code 目录不存在")
        return False
    
    server_py = code_path / "server.py"
    if not server_py.exists():
        print("❌ server.py 不存在")
        return False
    
    print("✅ 核心文件存在")
    
    # 检查 .env 文件
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env 配置文件存在")
    else:
        print("⚠️  .env 配置文件不存在，将使用默认配置")
    
    return True

def get_llm_provider():
    """获取 LLM 提供商配置"""
    print("\n🤖 LLM 配置选择:")
    print("1. Ollama (本地)")
    print("2. SiliconFlow (第三方平台)")
    print("3. OpenAI (官方 API)")
    print("4. LMStudio (本地 API)")
    
    while True:
        choice = input("请选择 LLM 提供商 (1-4, 默认 1): ").strip()
        if not choice:
            choice = "1"
        
        if choice == "1":
            return "ollama", "hf.co/bartowski/huihui-ai_Mistral-Small-24B-Instruct-2501-abliterated-GGUF:Q4_K_M"
        elif choice == "2":
            return "siliconflow", "Qwen/QwQ-32B"
        elif choice == "3":
            return "openai", "gpt-3.5-turbo"
        elif choice == "4":
            return "lmstudio", "local-model"
        else:
            print("无效选择，请重新输入")

def get_tts_engine():
    """获取 TTS 引擎配置"""
    print("\n🔊 TTS 引擎选择:")
    print("1. Kokoro (快速，英语)")
    print("2. Coqui (高质量，多语言)")
    print("3. Orpheus (最高质量，慢)")
    
    while True:
        choice = input("请选择 TTS 引擎 (1-3, 默认 1): ").strip()
        if not choice:
            choice = "1"
        
        if choice == "1":
            return "kokoro"
        elif choice == "2":
            return "coqui"
        elif choice == "3":
            return "orpheus"
        else:
            print("无效选择，请重新输入")

def update_server_config(llm_provider, llm_model, tts_engine):
    """更新服务器配置"""
    server_py_path = Path("code/server.py")
    
    try:
        with open(server_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新 LLM 配置
        content = content.replace(
            'LLM_START_PROVIDER = "ollama"',
            f'LLM_START_PROVIDER = "{llm_provider}"'
        )
        content = content.replace(
            'LLM_START_MODEL = "hf.co/bartowski/huihui-ai_Mistral-Small-24B-Instruct-2501-abliterated-GGUF:Q4_K_M"',
            f'LLM_START_MODEL = "{llm_model}"'
        )
        
        # 更新 TTS 配置
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('TTS_START_ENGINE = '):
                lines[i] = f'TTS_START_ENGINE = "{tts_engine}"'
                break
        
        content = '\n'.join(lines)
        
        with open(server_py_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 服务器配置已更新")
        
    except Exception as e:
        print(f"⚠️  更新配置失败: {e}")

def start_server():
    """启动服务器"""
    print("\n🚀 启动服务器...")
    print("服务器将在 http://localhost:8000 运行")
    print("按 Ctrl+C 停止服务器")
    print("-" * 40)
    
    try:
        # 切换到 code 目录并启动服务器
        os.chdir("code")
        subprocess.run([sys.executable, "server.py"], check=True)
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 服务器启动失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 意外错误: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_python_version():
        sys.exit(1)
    
    if not check_virtual_environment():
        sys.exit(1)
    
    if not check_dependencies():
        print("\n请先安装依赖:")
        print("  pip install -r requirements.txt")
        sys.exit(1)
    
    if not check_config_files():
        sys.exit(1)
    
    print("\n✅ 环境检查完成")
    
    # 获取用户配置
    llm_provider, llm_model = get_llm_provider()
    tts_engine = get_tts_engine()
    
    # 更新配置
    update_server_config(llm_provider, llm_model, tts_engine)
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
