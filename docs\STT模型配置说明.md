# STT（语音转文本）模型配置说明

## 1. 概述

RealtimeVoiceChat 使用 RealtimeSTT 库进行实时语音识别，支持多种 Whisper 模型和配置选项。本文档详细说明如何配置和切换不同的 STT 模型。

## 2. 支持的模型

### 2.1 Whisper 模型类型

项目支持以下 Whisper 模型：

| 模型名称 | 大小 | 内存需求 | 速度 | 准确性 | 推荐用途 |
|---------|------|----------|------|--------|----------|
| `tiny` | ~39MB | ~1GB | 最快 | 较低 | 快速测试 |
| `tiny.en` | ~39MB | ~1GB | 最快 | 较低 | 英语快速测试 |
| `base` | ~74MB | ~1GB | 快 | 中等 | 一般使用 |
| `base.en` | ~74MB | ~1GB | 快 | 中等 | 英语一般使用 |
| `small` | ~244MB | ~2GB | 中等 | 好 | 平衡性能 |
| `small.en` | ~244MB | ~2GB | 中等 | 好 | 英语平衡性能 |
| `medium` | ~769MB | ~5GB | 慢 | 很好 | 高质量识别 |
| `medium.en` | ~769MB | ~5GB | 慢 | 很好 | 英语高质量 |
| `large` | ~1550MB | ~10GB | 最慢 | 最好 | 最高质量 |
| `large-v2` | ~1550MB | ~10GB | 最慢 | 最好 | 最新大模型 |
| `large-v3` | ~1550MB | ~10GB | 最慢 | 最好 | 最新大模型 |

### 2.2 模型选择建议

**CPU 用户：**
- 推荐：`base.en`（英语）或 `base`（多语言）
- 备选：`tiny.en` 或 `tiny`（如果性能不足）

**GPU 用户：**
- 推荐：`small.en`（英语）或 `small`（多语言）
- 高端GPU：`medium.en` 或 `medium`

**高精度需求：**
- 推荐：`large-v3` 或 `large-v2`（需要强大的硬件）

## 3. 配置方法

### 3.1 修改配置文件

STT 配置位于 `code/transcribe.py` 文件中的 `DEFAULT_RECORDER_CONFIG` 字典：

```python
DEFAULT_RECORDER_CONFIG: Dict[str, Any] = {
    "model": "base.en",                    # 主要模型
    "realtime_model_type": "base.en",      # 实时识别模型
    "language": "en",                      # 语言设置
    "use_main_model_for_realtime": False,  # 是否使用主模型进行实时识别
    # ... 其他配置
}
```

### 3.2 主要配置参数

#### 3.2.1 模型设置

```python
# 主要识别模型（用于最终转录）
"model": "base.en",

# 实时识别模型（用于实时显示）
"realtime_model_type": "base.en",

# 是否使用主模型进行实时识别（提高准确性但增加延迟）
"use_main_model_for_realtime": False,
```

#### 3.2.2 语言设置

```python
# 语言代码（"en", "zh", "ja", "ko", "auto" 等）
"language": "en",

# 初始提示词（帮助模型理解上下文）
"initial_prompt_realtime": "The sky is blue. When the sky... She walked home. Because he... Today is sunny. If only I...",
```

#### 3.2.3 音频处理参数

```python
# 静音检测敏感度（0.01-1.0，越小越敏感）
"silero_sensitivity": 0.05,

# WebRTC 语音活动检测敏感度（0-3）
"webrtc_sensitivity": 3,

# 语音结束后的静音持续时间（秒）
"post_speech_silence_duration": 0.7,

# 最小录音长度（秒）
"min_length_of_recording": 0.5,

# 录音间隔最小间隔（秒）
"min_gap_between_recordings": 0,
```

#### 3.2.4 转录质量参数

```python
# Beam search 大小（1-5，越大越准确但越慢）
"beam_size": 3,
"beam_size_realtime": 3,

# 是否启用 VAD 过滤
"faster_whisper_vad_filter": False,

# 静音时提前转录（秒，0 表示禁用）
"early_transcription_on_silence": 0,
```

## 4. 常用配置示例

### 4.1 英语高性能配置

```python
DEFAULT_RECORDER_CONFIG = {
    "model": "small.en",
    "realtime_model_type": "base.en",
    "language": "en",
    "use_main_model_for_realtime": False,
    "silero_sensitivity": 0.05,
    "webrtc_sensitivity": 3,
    "post_speech_silence_duration": 0.5,
    "beam_size": 5,
    "beam_size_realtime": 3,
    # ... 其他默认配置
}
```

### 4.2 中文配置

```python
DEFAULT_RECORDER_CONFIG = {
    "model": "small",
    "realtime_model_type": "base",
    "language": "zh",
    "use_main_model_for_realtime": False,
    "silero_sensitivity": 0.08,
    "webrtc_sensitivity": 2,
    "post_speech_silence_duration": 0.8,
    "beam_size": 3,
    "beam_size_realtime": 1,
    "initial_prompt_realtime": "你好，今天天气很好。我们来聊聊吧。",
    # ... 其他默认配置
}
```

### 4.3 多语言自动检测配置

```python
DEFAULT_RECORDER_CONFIG = {
    "model": "medium",
    "realtime_model_type": "small",
    "language": "auto",  # 自动检测语言
    "use_main_model_for_realtime": False,
    "silero_sensitivity": 0.06,
    "webrtc_sensitivity": 3,
    "post_speech_silence_duration": 0.7,
    "beam_size": 3,
    "beam_size_realtime": 2,
    # ... 其他默认配置
}
```

### 4.4 低延迟配置

```python
DEFAULT_RECORDER_CONFIG = {
    "model": "tiny.en",
    "realtime_model_type": "tiny.en",
    "language": "en",
    "use_main_model_for_realtime": True,  # 使用同一模型减少切换开销
    "silero_sensitivity": 0.03,  # 更敏感的静音检测
    "webrtc_sensitivity": 3,
    "post_speech_silence_duration": 0.3,  # 更短的静音等待
    "min_length_of_recording": 0.3,
    "beam_size": 1,  # 最小 beam size
    "beam_size_realtime": 1,
    "realtime_processing_pause": 0.01,  # 更频繁的处理
    # ... 其他默认配置
}
```

## 5. 高级配置

### 5.1 唤醒词配置

```python
# 启用唤醒词功能
"wake_words": "jarvis",  # 唤醒词
"wakeword_backend": "pvporcupine",  # 唤醒词后端

# 或者禁用唤醒词
"wake_words": "",
```

### 5.2 性能优化配置

```python
# 使用 ONNX 加速（推荐）
"silero_use_onnx": True,

# 启用去活动检测
"silero_deactivity_detection": True,

# 实时处理暂停时间（秒）
"realtime_processing_pause": 0.03,

# 允许的延迟限制（毫秒）
"allowed_latency_limit": 500,
```

## 6. 应用配置更改

### 6.1 修改配置文件

1. 编辑 `code/transcribe.py` 文件
2. 找到 `DEFAULT_RECORDER_CONFIG` 字典
3. 修改相应的配置参数
4. 保存文件

### 6.2 重启应用

```powershell
# 停止当前运行的应用（Ctrl+C）
# 重新启动
cd code
python server.py
```

### 6.3 验证配置

启动应用后，查看控制台输出确认模型加载：

```
🎤⚙️ Initializing STT with model: base.en
🎤🔌 Using device: cuda
🎤✅ STT model loaded successfully
```

## 7. 故障排除

### 7.1 常见问题

**模型下载失败：**
- 检查网络连接
- 确保有足够的磁盘空间
- 尝试使用代理或 VPN

**内存不足：**
- 使用更小的模型（如 `tiny` 或 `base`）
- 关闭其他占用内存的应用程序
- 考虑使用 CPU 模式

**识别准确性低：**
- 使用更大的模型（如 `small` 或 `medium`）
- 调整 `beam_size` 参数
- 检查麦克风质量和环境噪音

**延迟过高：**
- 使用更小的模型
- 减少 `beam_size`
- 调整 `post_speech_silence_duration`

### 7.2 性能调优

**CPU 优化：**
- 使用 `.en` 版本的模型（如果只需要英语）
- 设置较小的 `beam_size`
- 启用 `silero_use_onnx`

**GPU 优化：**
- 确保 CUDA 可用
- 使用适合 GPU 内存的模型大小
- 监控 GPU 内存使用情况

## 8. 环境变量配置

除了代码配置，还可以通过环境变量进行一些设置：

```env
# .env 文件中添加
STT_MODEL=base.en
STT_LANGUAGE=en
STT_DEVICE=cuda  # 或 cpu
```

然后在代码中读取这些环境变量来动态配置模型。
