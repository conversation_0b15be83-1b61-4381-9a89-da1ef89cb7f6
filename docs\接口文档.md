# RealtimeVoiceChat 接口文档

## 1. WebSocket 接口

RealtimeVoiceChat 主要通过 WebSocket 进行实时通信，以下是主要的 WebSocket 接口：

### 1.1 WebSocket 连接

**连接地址**：`ws://{服务器地址}/ws`

**描述**：建立与服务器的 WebSocket 连接，用于音频数据传输和控制消息交换。

### 1.2 客户端到服务器消息

#### 1.2.1 音频数据

**格式**：二进制数据

**描述**：客户端捕获的原始音频数据，以二进制格式发送。音频数据应为 16-bit PCM 格式，采样率为 48kHz。

#### 1.2.2 控制消息

**格式**：JSON 对象

**描述**：客户端发送的控制命令，包括以下类型：

##### 初始化消息

```json
{
  "type": "init",
  "sampleRate": 48000,
  "ttsEngine": "kokoro",
  "llmProvider": "ollama",
  "llmModel": "llama3",
  "ttsVoice": "v2/en_US/cori/medium"
}
```

- `type`：消息类型，固定为 "init"
- `sampleRate`：客户端音频采样率（Hz）
- `ttsEngine`：文本转语音引擎（可选值："kokoro", "coqui", "orpheus"）
- `llmProvider`：大型语言模型提供商（可选值："ollama", "openai", "lmstudio"）
- `llmModel`：使用的模型名称
- `ttsVoice`：TTS 语音选项

##### 重置消息

```json
{
  "type": "reset"
}
```

- `type`：消息类型，固定为 "reset"

**描述**：重置当前对话，清除对话历史。

##### 停止生成消息

```json
{
  "type": "stop"
}
```

- `type`：消息类型，固定为 "stop"

**描述**：停止当前正在进行的 AI 响应生成。

##### 设置消息

```json
{
  "type": "settings",
  "settings": {
    "ttsEngine": "kokoro",
    "ttsVoice": "v2/en_US/cori/medium",
    "llmProvider": "ollama",
    "llmModel": "llama3"
  }
}
```

- `type`：消息类型，固定为 "settings"
- `settings`：设置对象，包含要更新的设置

**描述**：更新系统设置。

### 1.3 服务器到客户端消息

#### 1.3.1 音频数据

**格式**：二进制数据

**描述**：服务器生成的 TTS 音频数据，以二进制格式发送。音频数据为 16-bit PCM 格式，采样率为 24kHz。

#### 1.3.2 控制消息

**格式**：JSON 对象

**描述**：服务器发送的状态和控制信息，包括以下类型：

##### 转录更新消息

```json
{
  "type": "transcript",
  "text": "用户说的话的实时转录",
  "final": false
}
```

- `type`：消息类型，固定为 "transcript"
- `text`：转录的文本内容
- `final`：是否为最终转录结果

**描述**：实时语音转文本的转录结果。

##### 文本消息

```json
{
  "type": "text",
  "text": "AI 生成的文本响应",
  "final": false
}
```

- `type`：消息类型，固定为 "text"
- `text`：AI 生成的文本内容
- `final`：是否为最终文本结果

**描述**：AI 生成的文本响应。

##### 状态消息

```json
{
  "type": "status",
  "status": "recording"
}
```

- `type`：消息类型，固定为 "status"
- `status`：当前状态（可能的值："idle", "recording", "processing", "speaking"）

**描述**：系统当前状态的更新。

##### 错误消息

```json
{
  "type": "error",
  "message": "错误描述"
}
```

- `type`：消息类型，固定为 "error"
- `message`：错误描述

**描述**：系统错误信息。

## 2. HTTP API

除了 WebSocket 接口外，系统还提供了一些 HTTP API 端点：

### 2.1 获取可用模型

**请求**：`GET /api/models`

**响应**：
```json
{
  "llm": {
    "ollama": ["llama3", "mistral", "...其他可用模型"],
    "openai": ["gpt-4", "gpt-3.5-turbo", "...其他可用模型"],
    "lmstudio": []
  },
  "tts": {
    "kokoro": ["v2/en_US/cori/medium", "...其他可用语音"],
    "coqui": ["...可用语音"],
    "orpheus": ["...可用语音"]
  }
}
```

**描述**：获取系统中可用的 LLM 模型和 TTS 语音选项。

### 2.2 获取系统状态

**请求**：`GET /api/status`

**响应**：
```json
{
  "status": "idle",
  "llm_connected": true,
  "tts_ready": true,
  "stt_ready": true
}
```

**描述**：获取系统当前状态。

### 2.3 获取系统设置

**请求**：`GET /api/settings`

**响应**：
```json
{
  "tts_engine": "kokoro",
  "tts_voice": "v2/en_US/cori/medium",
  "llm_provider": "ollama",
  "llm_model": "llama3",
  "system_prompt": "你是一个有帮助的助手..."
}
```

**描述**：获取当前系统设置。

### 2.4 更新系统设置

**请求**：`POST /api/settings`

**请求体**：
```json
{
  "tts_engine": "kokoro",
  "tts_voice": "v2/en_US/cori/medium",
  "llm_provider": "ollama",
  "llm_model": "llama3",
  "system_prompt": "你是一个有帮助的助手..."
}
```

**响应**：
```json
{
  "success": true
}
```

**描述**：更新系统设置。

## 3. 前端 JavaScript API

前端 JavaScript 代码提供了以下主要 API 和函数：

### 3.1 音频捕获

```javascript
startRawPcmCapture()
```

**描述**：开始捕获麦克风音频并发送到服务器。

```javascript
stopRawPcmCapture()
```

**描述**：停止捕获麦克风音频。

### 3.2 TTS 播放

```javascript
setupTTSPlayback()
```

**描述**：设置 TTS 音频播放。

```javascript
playTTSAudio(audioData)
```

**描述**：播放 TTS 生成的音频数据。

### 3.3 WebSocket 通信

```javascript
connectWebSocket()
```

**描述**：建立与服务器的 WebSocket 连接。

```javascript
sendJsonMessage(message)
```

**描述**：发送 JSON 格式的控制消息到服务器。

### 3.4 UI 交互

```javascript
updateTranscript(text, final)
```

**描述**：更新转录文本显示。

```javascript
updateAIResponse(text, final)
```

**描述**：更新 AI 响应文本显示。

```javascript
updateStatus(status)
```

**描述**：更新系统状态显示。

## 4. 通信流程

### 4.1 典型对话流程

1. 客户端通过 WebSocket 连接到服务器
2. 客户端发送初始化消息，设置采样率和模型选项
3. 用户开始说话，客户端捕获音频并发送到服务器
4. 服务器实时处理音频，返回转录更新
5. 当检测到用户停止说话，服务器生成 AI 响应
6. 服务器将 AI 文本响应和 TTS 音频流发送回客户端
7. 客户端显示文本响应并播放 TTS 音频

### 4.2 中断处理流程

1. 用户在 AI 响应过程中开始说话
2. 客户端发送新的音频数据到服务器
3. 服务器检测到新的用户输入，停止当前 AI 响应生成
4. 服务器处理新的用户输入，生成新的 AI 响应

## 5. 错误处理

### 5.1 常见错误代码

- `connection_error`：WebSocket 连接错误
- `audio_error`：音频处理错误
- `llm_error`：LLM 模型错误
- `tts_error`：TTS 引擎错误
- `stt_error`：STT 引擎错误

### 5.2 错误恢复策略

- WebSocket 连接错误：客户端自动尝试重新连接
- 模型加载错误：服务器尝试回退到备用模型
- 音频处理错误：重置音频处理管道

## 6. 性能考虑

### 6.1 延迟优化

- 使用小块音频数据（通常为 4096 字节）进行流式传输
- 使用二进制格式而非 Base64 编码传输音频数据
- 实现增量处理和流式响应

### 6.2 带宽使用

- 音频输入：约 96 KB/s（48kHz, 16-bit, mono）
- 音频输出：约 48 KB/s（24kHz, 16-bit, mono）
- 控制消息：通常小于 1 KB

### 6.3 资源使用

- GPU 内存：取决于所使用的模型（通常 2-8 GB）
- CPU 使用率：中等到高（取决于是否使用 GPU 加速）
- RAM 使用：约 2-4 GB（取决于模型大小）