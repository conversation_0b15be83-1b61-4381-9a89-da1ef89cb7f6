# RealtimeVoiceChat 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 基本配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 音频队列大小限制
MAX_AUDIO_QUEUE_SIZE=50

# =============================================================================
# LLM 配置 - 选择一个提供商并配置相应的参数
# =============================================================================

# SiliconFlow 配置 (推荐 - 成本低，模型丰富)
SILICONFLOW_API_KEY=your_siliconflow_api_key_here
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# OpenAI 配置
OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_BASE_URL=https://api.openai.com/v1  # 可选，使用自定义端点

# Ollama 配置 (本地运行)
OLLAMA_BASE_URL=http://127.0.0.1:11434

# LMStudio 配置 (本地 API 服务)
LMSTUDIO_BASE_URL=http://127.0.0.1:1234/v1

# =============================================================================
# 其他第三方平台配置示例
# =============================================================================

# Together AI
# OPENAI_API_KEY=your_together_api_key
# OPENAI_BASE_URL=https://api.together.xyz/v1

# Groq
# OPENAI_API_KEY=your_groq_api_key
# OPENAI_BASE_URL=https://api.groq.com/openai/v1

# DeepSeek
# OPENAI_API_KEY=your_deepseek_api_key
# OPENAI_BASE_URL=https://api.deepseek.com/v1

# =============================================================================
# 高级配置 (可选)
# =============================================================================

# CUDA 设备配置 (如果有多个 GPU)
# CUDA_VISIBLE_DEVICES=0

# HuggingFace 缓存目录
# HF_HOME=/path/to/huggingface/cache

# PyTorch 缓存目录
# TORCH_HOME=/path/to/torch/cache

# =============================================================================
# 开发配置 (开发者使用)
# =============================================================================

# 启用调试模式
# DEBUG=true

# 禁用模型预热 (加快启动速度，但首次推理较慢)
# DISABLE_PREWARM=true

# 强制使用 CPU 模式
# FORCE_CPU=true

# =============================================================================
# 配置说明
# =============================================================================

# 1. SiliconFlow 配置 (推荐新用户)
#    - 注册账户：https://api.siliconflow.cn
#    - 获取 API 密钥
#    - 设置 SILICONFLOW_API_KEY
#    - 在 code/server.py 中设置 LLM_START_PROVIDER = "siliconflow"

# 2. OpenAI 配置
#    - 获取 OpenAI API 密钥：https://platform.openai.com/api-keys
#    - 设置 OPENAI_API_KEY
#    - 在 code/server.py 中设置 LLM_START_PROVIDER = "openai"

# 3. Ollama 配置 (本地运行)
#    - 安装 Ollama：https://ollama.ai/download
#    - 拉取模型：ollama pull llama3
#    - 确保 Ollama 服务运行在 11434 端口
#    - 在 code/server.py 中设置 LLM_START_PROVIDER = "ollama"

# 4. LMStudio 配置
#    - 安装 LMStudio：https://lmstudio.ai/
#    - 加载模型并启动本地服务器
#    - 确保服务运行在 1234 端口
#    - 在 code/server.py 中设置 LLM_START_PROVIDER = "lmstudio"
