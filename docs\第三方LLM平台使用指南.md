# 第三方 LLM 平台使用指南

## 1. 概述

RealtimeVoiceChat 支持多种 LLM 后端，包括本地 Ollama、OpenAI API 以及第三方平台。本文档重点介绍如何配置和使用第三方 LLM 平台，特别是 SiliconFlow 等兼容 OpenAI API 的平台。

## 2. 支持的 LLM 后端

### 2.1 后端类型对比

| 后端类型 | 特点 | 成本 | 延迟 | 模型选择 | 隐私性 |
|----------|------|------|------|----------|--------|
| **Ollama** | 本地运行 | 免费 | 低 | 有限 | 最高 |
| **OpenAI** | 官方 API | 按使用付费 | 中等 | 丰富 | 中等 |
| **第三方平台** | API 服务 | 通常更便宜 | 中等 | 很丰富 | 中等 |
| **LMStudio** | 本地 API 服务 | 免费 | 低 | 有限 | 最高 |

### 2.2 第三方平台优势

- **成本效益**：通常比 OpenAI 官方 API 更便宜
- **模型丰富**：支持多种开源和商业模型
- **易于使用**：兼容 OpenAI API 格式
- **无需本地资源**：不需要强大的本地硬件

## 3. SiliconFlow 平台配置

### 3.1 获取 API 密钥

1. 访问 [SiliconFlow 官网](https://api.siliconflow.cn)
2. 注册账户并登录
3. 在控制台中创建 API 密钥
4. 记录您的 API 密钥

### 3.2 配置环境变量

在项目根目录的 `.env` 文件中添加：

```env
# SiliconFlow 配置
SILICONFLOW_API_KEY=your_api_key_here
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1
```

### 3.3 修改代码配置

#### 3.3.1 添加 SiliconFlow 支持

项目已经包含了 `custom_llm_provider.py` 文件，支持 SiliconFlow 平台。

#### 3.3.2 修改服务器配置

在 `code/server.py` 中修改 LLM 配置：

```python
# 使用 SiliconFlow 平台
LLM_START_PROVIDER = "siliconflow"
LLM_START_MODEL = "Qwen/QwQ-32B"  # 或其他支持的模型
```

#### 3.3.3 验证集成

项目已经集成了 SiliconFlow 支持，包括：

1. **自定义 LLM 提供者** (`custom_llm_provider.py`)：
   - 完整的 SiliconFlow API 客户端
   - 流式响应处理
   - 错误处理和重试机制

2. **管道集成** (`speech_pipeline_manager.py`)：
   - 自动检测 `siliconflow` 提供者
   - 无缝切换到 SiliconFlow 客户端
   - 保持与其他提供者相同的接口

3. **启动脚本**：
   - `start_app.py`：Python 启动脚本，支持交互式配置
   - `start_app.bat`：Windows 批处理脚本，一键启动

### 3.4 快速启动

**方法一：使用启动脚本（推荐）**

```powershell
# Windows 用户
.\start_app.bat

# 或使用 Python 脚本
python start_app.py
```

启动脚本会：
1. 检查环境和依赖
2. 提供交互式配置选择
3. 自动更新配置文件
4. 启动应用服务器

**方法二：手动配置**

1. 配置环境变量：
```env
SILICONFLOW_API_KEY=your_api_key_here
```

2. 修改 `code/server.py`：
```python
LLM_START_PROVIDER = "siliconflow"
LLM_START_MODEL = "Qwen/QwQ-32B"
```

3. 启动应用：
```powershell
cd code
python server.py
```

## 4. 支持的模型

### 4.1 SiliconFlow 热门模型

| 模型名称 | 特点 | 适用场景 | 成本 |
|----------|------|----------|------|
| `Qwen/QwQ-32B` | 推理能力强 | 复杂对话 | 中等 |
| `meta-llama/Llama-3.1-8B-Instruct` | 平衡性能 | 一般对话 | 低 |
| `meta-llama/Llama-3.1-70B-Instruct` | 高性能 | 专业对话 | 高 |
| `deepseek-ai/DeepSeek-V2.5` | 代码能力强 | 技术对话 | 中等 |
| `01-ai/Yi-1.5-34B-Chat` | 中文优化 | 中文对话 | 中等 |

### 4.2 模型选择建议

**日常对话：**
```python
LLM_START_MODEL = "meta-llama/Llama-3.1-8B-Instruct"
```

**复杂推理：**
```python
LLM_START_MODEL = "Qwen/QwQ-32B"
```

**中文对话：**
```python
LLM_START_MODEL = "01-ai/Yi-1.5-34B-Chat"
```

**代码相关：**
```python
LLM_START_MODEL = "deepseek-ai/DeepSeek-V2.5"
```

## 5. API 参数配置

### 5.1 基本参数

```python
# 在 custom_llm_provider.py 的 generate 方法中
payload = {
    "model": self.model,
    "messages": messages,
    "stream": True,
    "temperature": 0.7,        # 创造性 (0.0-2.0)
    "top_p": 0.7,             # 核采样 (0.0-1.0)
    "max_tokens": 512,        # 最大输出长度
    "enable_thinking": True,   # 启用思考模式
    **kwargs
}
```

### 5.2 高级参数

```python
# 可以通过 kwargs 传递的额外参数
{
    "frequency_penalty": 0.5,  # 频率惩罚
    "presence_penalty": 0.0,   # 存在惩罚
    "stop": ["Human:", "AI:"], # 停止词
    "thinking_budget": 4096,   # 思考预算
    "min_p": 0.05,            # 最小概率
    "top_k": 50,              # Top-K 采样
}
```

### 5.3 针对不同场景的参数调整

#### 5.3.1 创意对话配置

```python
{
    "temperature": 0.9,
    "top_p": 0.9,
    "frequency_penalty": 0.3,
    "presence_penalty": 0.1,
}
```

#### 5.3.2 事实性对话配置

```python
{
    "temperature": 0.3,
    "top_p": 0.5,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0,
}
```

#### 5.3.3 代码生成配置

```python
{
    "temperature": 0.1,
    "top_p": 0.3,
    "max_tokens": 1024,
    "stop": ["```", "Human:"],
}
```

## 6. 其他第三方平台

### 6.1 通用 OpenAI 兼容平台

大多数第三方平台都兼容 OpenAI API 格式，可以通过修改 base_url 来使用：

```python
# 在 llm_module.py 中使用 OpenAI 后端
LLM_START_PROVIDER = "openai"
LLM_START_MODEL = "your-model-name"

# 在 .env 文件中设置
OPENAI_API_KEY=your_api_key
OPENAI_BASE_URL=https://your-platform-api-url/v1
```

### 6.2 常见第三方平台

#### 6.2.1 Together AI

```env
OPENAI_API_KEY=your_together_api_key
OPENAI_BASE_URL=https://api.together.xyz/v1
```

#### 6.2.2 Groq

```env
OPENAI_API_KEY=your_groq_api_key
OPENAI_BASE_URL=https://api.groq.com/openai/v1
```

#### 6.2.3 Anthropic Claude (通过代理)

```env
OPENAI_API_KEY=your_anthropic_api_key
OPENAI_BASE_URL=https://api.anthropic.com/v1
```

## 7. 完整配置示例

### 7.1 SiliconFlow 完整配置

**1. 环境变量 (.env):**
```env
SILICONFLOW_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1
LOG_LEVEL=INFO
```

**2. 服务器配置 (server.py):**
```python
LLM_START_PROVIDER = "siliconflow"
LLM_START_MODEL = "Qwen/QwQ-32B"
NO_THINK = False
```

**3. 启动应用:**
```powershell
cd code
python server.py
```

### 7.2 验证配置

启动应用后，查看控制台输出：

```
🤖⚙️ Initializing SiliconFlow LLM with model: Qwen/QwQ-32B
🤖🔌 Successfully connected to SiliconFlow API
🤖✅ Connection initialized successfully for SiliconFlow API
```

## 8. 成本优化

### 8.1 选择合适的模型

- **日常使用**：选择较小的模型（如 8B 参数）
- **专业用途**：选择较大的模型（如 70B 参数）
- **测试阶段**：使用免费额度或最便宜的模型

### 8.2 参数优化

```python
# 减少 token 使用
"max_tokens": 256,        # 限制输出长度
"temperature": 0.3,       # 降低随机性，减少重试

# 优化系统提示词
system_prompt = "简洁回答，不要重复。"  # 要求简洁回答
```

### 8.3 监控使用量

定期检查 API 使用量和费用：
- 在平台控制台查看使用统计
- 设置使用量警报
- 定期审查对话日志

## 9. 故障排除

### 9.1 常见错误

**API 密钥错误：**
```
🤖💥 Connection Error: 401 Unauthorized
```
解决：检查 API 密钥是否正确

**模型不存在：**
```
🤖💥 API Error: Model not found
```
解决：确认模型名称正确，检查平台支持的模型列表

**配额超限：**
```
🤖💥 Rate limit exceeded
```
解决：等待配额重置或升级账户

**网络连接问题：**
```
🤖💥 Connection timeout
```
解决：检查网络连接，可能需要使用代理

### 9.2 调试技巧

启用详细日志：

```python
# 在 .env 文件中
LOG_LEVEL=DEBUG
```

这将显示详细的 API 请求和响应信息。

### 9.3 备用方案

配置多个后端作为备用：

```python
# 主要后端
LLM_START_PROVIDER = "siliconflow"

# 在代码中添加备用逻辑
try:
    # 尝试主要后端
    response = primary_llm.generate(text)
except Exception:
    # 切换到备用后端
    response = backup_llm.generate(text)
```

## 10. 最佳实践

### 10.1 安全性

- 不要在代码中硬编码 API 密钥
- 使用环境变量存储敏感信息
- 定期轮换 API 密钥
- 监控异常使用模式

### 10.2 性能优化

- 选择地理位置较近的 API 端点
- 使用连接池减少连接开销
- 实现请求重试机制
- 缓存常见响应

### 10.3 用户体验

- 实现优雅的错误处理
- 提供备用响应机制
- 显示适当的加载状态
- 记录用户反馈以优化模型选择
