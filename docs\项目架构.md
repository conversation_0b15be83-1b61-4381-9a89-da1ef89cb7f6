# RealtimeVoiceChat 项目架构

## 1. 整体架构

RealtimeVoiceChat 是一个实时语音聊天系统，允许用户通过语音与大型语言模型（LLM）进行自然对话。系统采用客户端-服务器架构，主要由以下几个部分组成：

### 1.1 前端（Web 客户端）

- 基于原生 JavaScript、HTML 和 CSS 构建的 Web 界面
- 使用 Web Audio API 和 AudioWorklets 处理音频输入和输出
- 通过 WebSocket 与后端服务器进行实时通信

### 1.2 后端（Python 服务器）

- 基于 FastAPI 构建的 Web 服务器
- 语音处理管道（Speech Pipeline）
- 大型语言模型（LLM）集成
- 实时语音转文本（STT）和文本转语音（TTS）处理

### 1.3 AI 模型

- 语音转文本（STT）：使用 RealtimeSTT 库（基于 Whisper）
- 文本转语音（TTS）：支持多种引擎（Kokoro、Coqui、Orpheus）
- 大型语言模型（LLM）：支持 Ollama、OpenAI 等后端

## 2. 核心组件详解

### 2.1 语音处理管道（SpeechPipelineManager）

`SpeechPipelineManager` 是系统的核心组件，负责协调语音输入、LLM 处理和语音输出的整个流程：

- 管理 LLM 请求队列和生成状态
- 协调快速回答和最终回答的生成
- 处理中断和恢复机制
- 维护对话历史

### 2.2 音频处理（AudioProcessor）

`AudioProcessor` 负责文本到语音的转换：

- 支持多种 TTS 引擎（Kokoro、Coqui、Orpheus）
- 处理音频流和合成参数
- 管理音频缓冲和回放

### 2.3 音频输入处理（AudioInputProcessor）

`AudioInputProcessor` 负责处理来自客户端的音频输入：

- 重采样音频数据
- 将音频数据传递给转录处理器
- 管理实时转录回调

### 2.4 转录处理（TranscriptionProcessor）

`TranscriptionProcessor` 使用 RealtimeSTT 库将语音转换为文本：

- 管理实时和最终转录结果
- 处理静音检测
- 支持潜在句子结束检测

### 2.5 对话轮次检测（TurnDetection）

`TurnDetection` 负责检测用户语音输入的自然停顿点：

- 使用 Transformer 模型预测句子完成概率
- 计算建议的等待时间
- 适应对话节奏

### 2.6 LLM 集成（LLM）

`LLM` 类提供了与各种大型语言模型后端的统一接口：

- 支持 Ollama（通过直接 HTTP）
- 支持 OpenAI API
- 支持 LMStudio（通过 OpenAI 兼容 API）
- 处理流式生成和请求取消

## 3. 数据流

系统的数据流如下：

1. **音频捕获**：浏览器捕获用户的语音
2. **音频流传输**：通过 WebSocket 将音频块传输到 Python 后端
3. **语音转文本**：`RealtimeSTT` 快速将语音转换为文本
4. **LLM 处理**：文本被发送到 LLM（如 Ollama 或 OpenAI）进行处理
5. **文本转语音**：AI 的文本响应使用 `RealtimeTTS` 转换回语音
6. **音频返回**：生成的音频流回到浏览器进行播放
7. **中断处理**：系统能够优雅地处理用户的中断

## 4. 技术栈

### 4.1 后端

- **语言**：Python < 3.13
- **Web 框架**：FastAPI
- **通信**：WebSockets
- **AI/ML 库**：
  - RealtimeSTT（语音转文本）
  - RealtimeTTS（文本转语音）
  - transformers（轮次检测、分词）
  - torch / torchaudio（ML 框架）
  - ollama / openai（LLM 客户端）
- **音频处理**：numpy、scipy

### 4.2 前端

- **语言**：JavaScript（原生）
- **音频处理**：Web Audio API、AudioWorklets
- **UI**：HTML、CSS

### 4.3 部署

- **容器化**：Docker、Docker Compose
- **GPU 支持**：CUDA、NVIDIA Container Toolkit（Linux）

## 5. 系统特点

- **流畅对话**：像真实对话一样说话和倾听
- **实时反馈**：实时显示部分转录和 AI 响应
- **低延迟**：使用音频块流式传输的优化架构
- **智能轮次**：动态静音检测适应对话节奏
- **灵活 AI**：可插拔 LLM 后端
- **可定制语音**：多种文本转语音引擎选择
- **Web 界面**：简洁的用户界面
- **Docker 部署**：使用 Docker Compose 简化依赖管理